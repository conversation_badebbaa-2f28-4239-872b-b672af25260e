const ffmpeg = require('fluent-ffmpeg');
const path = require('path');

let isConfigured = false;

function setupFFmpegPaths() {
  if (isConfigured) return;
  
  let ffmpegPath, ffprobePath;
  
  try {
    const { app } = require('electron');
    
    if (app && app.isPackaged) {
      ffmpegPath = path.join(process.resourcesPath, "static", "ffmpeg", 'ffmpeg');
      ffprobePath = path.join(process.resourcesPath, "static", "ffmpeg", "ffprobe");
    } else {
      const appPath = app ? app.getAppPath() : path.join(__dirname, '..');
      ffmpegPath = path.join(appPath, "static", "ffmpeg", "ffmpeg");
      ffprobePath = path.join(appPath, "static", "ffmpeg", 'ffprobe');
    }
  } catch (error) {
    // Fallback nếu không có electron app
    const appPath = path.join(__dirname, '..');
    ffmpegPath = path.join(appPath, "static", "ffmpeg", "ffmpeg");
    ffprobePath = path.join(appPath, "static", "ffmpeg", 'ffprobe');
  }
  
  // Thêm extension .exe cho Windows
  if (process.platform === 'win32') {
    ffmpegPath += '.exe';
    ffprobePath += '.exe';
  }
  
  ffmpeg.setFfmpegPath(ffmpegPath);
  ffmpeg.setFfprobePath(ffprobePath);
  
  isConfigured = true;
  
  console.log('FFmpeg configured:', { ffmpegPath, ffprobePath });
  
  return { ffmpegPath, ffprobePath };
}

// Tự động cấu hình khi module được import
setupFFmpegPaths();

module.exports = {
  setupFFmpegPaths,
  ffmpeg
};
