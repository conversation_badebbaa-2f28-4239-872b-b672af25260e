# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).




https://ai.google.dev/gemini-api/docs/models

GUI https://github.com/timminator/VideOCR
https://github.com/devmaxxing/videocr-PaddleOCR


2. Cài đặt SoX
Windows: Tải tại https://sourceforge.net/projects/sox/

macOS: brew install sox

Linux: sudo apt install sox libsox-fmt-all



{
    "workspace_id": "7278208573803446274",
    "smart_tool_type": 39,
    "scene": 3,
    "params": "{\"text\":\"Mặc định\",\"platform\":1}",
    "req_json": "{\"speaker\":\"BV560_streaming\",\"audio_config\":{\"speech_rate\":0,\"pitch_rate\":12},\"disable_caption\":true}"
}

{
    "workspace_id": "7278208573803446274",
    "smart_tool_type": 39,
    "scene": 3,
    "params": "{\"text\":\"Mặc định\",\"platform\":1}",
    "req_json": "{\"speaker\":\"BV560_streaming\",\"audio_config\":{\"speech_rate\":100,\"pitch_rate\":3},\"disable_caption\":true}"
}

BV560_streaming Anh dũng
BV562_streaming chí mai
vi_female_huong nữ phổ thông
BV421_vivn_streaming Nguồn nhỏ ngọt ngào
BV075_streaming Tin
BV074_streaming Hoạt Ngôn


speech_rate -50 - 100
pitch_rate -12 - 12