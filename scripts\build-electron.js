const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

async function buildElectron() {
  try {
    console.log('🚀 Building electron with esbuild...');
    
    // Ensure output directory exists
    const outDir = 'electron-build';
    if (!fs.existsSync(outDir)) {
      fs.mkdirSync(outDir, { recursive: true });
    }

    // Build main.js
    await esbuild.build({
      entryPoints: ['electron/main.js'],
      bundle: true,
      platform: 'node',
      target: 'node18',
      format: 'cjs',
      outfile: 'electron-build/main.js',
      external: [
        // Built-in Node.js modules
        'electron',
        'path',
        'fs',
        'fs/promises',
        'child_process',
        'https',
        'http',
        'crypto',
        'os',
        'util',
        'events',
        'stream',
        'url',
        'querystring',
        'dns',
        'net',
        'tls',
        'zlib',
        'readline',
        'worker_threads',
        'cluster',
        'dgram',
        'assert',
        'buffer',
        'constants',
        'module',
        'perf_hooks',
        'process',
        'punycode',
        'string_decoder',
        'timers',
        'tty',
        'v8',
        'vm',
        // Optional database drivers
        'pg',
        'mysql',
        'mysql2',
        'better-sqlite3',
        'sqlite3',
        'tedious',
        'oracledb',
        'pg-query-stream',
        // Optional playwright dependencies
        'playwright',
        'playwright-core',
        'chromium-bidi/lib/cjs/bidiMapper/BidiMapper',
        'chromium-bidi/lib/cjs/cdp/CdpConnection'
      ],
      minify: true,
      sourcemap: false,
      logLevel: 'info'
    });

    // Build preload.js
    await esbuild.build({
      entryPoints: ['electron/preload.js'],
      bundle: true,
      platform: 'node',
      target: 'node18',
      format: 'cjs',
      outfile: 'electron-build/preload.js',
      external: ['electron'],
      minify: true,
      sourcemap: false,
      logLevel: 'info'
    });

    console.log('✅ Electron build completed!');
    
    // Show file sizes
    const mainStats = fs.statSync('electron-build/main.js');
    const preloadStats = fs.statSync('electron-build/preload.js');
    
    console.log(`📦 main.js: ${(mainStats.size / 1024).toFixed(2)} KB`);
    console.log(`📦 preload.js: ${(preloadStats.size / 1024).toFixed(2)} KB`);
    
  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}

buildElectron();
