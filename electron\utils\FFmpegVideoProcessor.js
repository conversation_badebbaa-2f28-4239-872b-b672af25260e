const { exec, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');
const ffmpeg = require('fluent-ffmpeg');

const srtArray = [
  {
    index: 1,
    id: 1,
    text: '群演一天一千去不去',
    startTime: 0,
    endTime: 1.6800000000000002,
    start: '00:00:00,000',
    end: '00:00:01,680',
    translatedText: '<PERSON>ễn viên quần chúng một ngày một nghìn, đi không?',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3',
    duration: 0,
    isGenerated: false,
    isVoice: 2,
    audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3',
    audioDuration2: 2742.857,
    isGenerated2: true,
  },
  {
    index: 2,
    id: 2,
    text: '又想打我',
    startTime: 1.6800000000000002,
    endTime: 3.7,
    start: '00:00:01,680',
    end: '00:00:03,700',
    translatedText: 'Lại định đánh tôi nữa à?',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3',
    duration: 0,
    isGenerated: false,
    isVoice: 1,
    audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3',
    audioDuration1: 1464,
    isGenerated1: true,
  },
];
const videoPath = 'I:\\ReviewDao\\con-nho\\1111.mp4';
let logoPath = '';
const tempDir = path.join(os.tmpdir(), 'video_segments');
const options = {
  addLogo: false,
  addText: false,
  customText: 'Video Rendered by Vue+Electron',
};

async function getVideoDuration(videoPath) {
  return new Promise((resolve, reject) => {
    const ffprobe = spawn('ffprobe', [
      '-v',
      'error',
      '-show_entries',
      'format=duration',
      '-of',
      'default=noprint_wrappers=1:nokey=1',
      videoPath,
    ]);

    let output = '';

    ffprobe.stdout.on('data', (data) => {
      output += data.toString();
    });

    ffprobe.stderr.on('data', (data) => {
      console.error('ffprobe stderr:', data.toString());
    });

    ffprobe.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`ffprobe process exited with code ${code}`));
        return;
      }

      const duration = parseFloat(output.trim());
      if (isNaN(duration)) {
        reject(new Error('Failed to parse video duration'));
        return;
      }

      resolve(duration);
    });
  });
}

// Tính thời lượng audio từ audioUrl
const getAudioDuration = (audioUrl) => {
  return new Promise((resolve, reject) => {
    const cleanAudioUrl = audioUrl.replace('file://', '');
    const command = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${cleanAudioUrl}"`;
    exec(command, (error, stdout) => {
      if (error) reject(error);
      else resolve(parseFloat(stdout));
    });
  });
};

// Chuyển đổi thời gian từ giây sang định dạng HH:MM:SS.mmm
const secondsToTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = (seconds % 60).toFixed(3);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// Cắt video theo startTime và endTime
const cutVideoSegment = async (srt, outputPath) => {
  const start = parseFloat(srt.startTime);
  const end = parseFloat(srt.endTime);

  const videoDuration = await getVideoDuration(videoPath);

  if (isNaN(start) || isNaN(end) || start >= videoDuration) {
    console.warn(`[cutVideoSegment] Skipping invalid segment: start=${start}, end=${end}, video=${videoDuration}`);
    return null;
  }

  const safeEnd = Math.min(end, videoDuration);
  const duration = parseFloat((safeEnd - start).toFixed(3));

  if (duration <= 0) {
    console.warn(`[cutVideoSegment] Skipping zero-duration segment: start=${start}, end=${safeEnd}`);
    return null;
  }

  const command = `ffmpeg -ss ${start} -t ${duration} -i "${videoPath}" -c:v libx264 -c:a aac -y "${outputPath}"`;
  //   console.log('[cutVideoSegment]', command);

  return new Promise((resolve, reject) => {
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};

// Tạo file ASS phụ đề
const createAssFile = (srtArray, assPath) => {
  const assContent = `[Script Info]
Title: Untitled
ScriptType: v4.00+
WrapStyle: 0
PlayResX: 384
PlayResY: 288

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,16,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
${srtArray
  .map(
    (srt) =>
      `Dialogue: 0,${srt.start.replace(/,/g, '.')},${srt.end.replace(
        /,/g,
        '.',
      )},Default,,0,0,0,,${srt.translatedText.replace(/\n/g, '\\N')}`,
  )
  .join('\n')}`;

  fs.writeFileSync(assPath, assContent);
};
function generateASS(subs) {
  const header = `
[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,&H00FFFFFF,&H000000FF,&H00000000,&H64000000,-1,0,0,0,100,100,0,0,1,2,0,2,20,20,30,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`.trim();

  const formatTime = (t) => {
    // Nếu t là "0:00:00.18", giữ nguyên
    // Nếu t là seconds: 1.5 → "0:00:01.50"
    if (typeof t === 'number') {
      const h = String(Math.floor(t / 3600)).padStart(1, '0');
      const m = String(Math.floor((t % 3600) / 60)).padStart(2, '0');
      const s = (t % 60).toFixed(2).padStart(5, '0');
      return `${h}:${m}:${s}`;
    }
    return t;
  };
  const toAssTime = (seconds) => {
    const h = String(Math.floor(seconds / 3600)).padStart(1, '0');
    const m = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const s = String((seconds % 60).toFixed(2))
      .padStart(5, '0')
      .replace('.', ':');
    return `${h}:${m}:${s}`;
  };
  const body = subs
    .map((srt) => {
      // const factor = srt.speedFactor || 1;
      // console.log('factor: ', factor);

      // const adjStart = srt.startTime * factor;
      // const adjEnd = srt.endTime * factor;
      return `Dialogue: 0,${formatTime(srt.adjustedStartTime)},${formatTime(
        srt.adjustedEndTime,
      )},Default,,0,0,0,,${srt.translatedText.replace(/\n/g, '\\N')}`;
    })
    .join('\n');

  return `${header}\n${body}`;
}
//Dialogue: 0,0:00:02.00,0:00:03.75,Default,,0,0,0,,Một người khiêng quan tài với 40 năm kinh nghiệm kể rằng

// Điều chỉnh tốc độ video nếu audio dài hơn
const adjustVideoSpeed = (videoSegment, audioUrl, srt, outputPath) => {
  return new Promise((resolve, reject) => {
    const audioPath = audioUrl.replace('file://', '');
    const videoDuration = srt.endTime - srt.startTime;
    const audioDuration = srt.duration;
    const speedFactor = audioDuration / videoDuration;
    srt.speedFactor = speedFactor < 1 ? speedFactor : 1;
    let command = '';

    if (speedFactor > 1) {
      // Audio dài hơn → làm chậm video để khớp
      command =
        `ffmpeg -i "${videoSegment}" -i "${audioPath}" ` +
        `-filter_complex "[0:v]setpts=${speedFactor}*PTS[v]" ` +
        `-map "[v]" -map 1:a -c:v h264_nvenc -c:a aac -y "${outputPath}"`;
    } else {
      // Audio ngắn hơn hoặc bằng → giữ nguyên video
      command =
        `ffmpeg -i "${videoSegment}" -i "${audioPath}" ` +
        `-map 0:v -map 1:a -c:v h264_nvenc -c:a aac -y "${outputPath}"`;
    }

    console.log('adjustVideoSpeed:', srt.speedFactor);
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};

// Thêm logo hoặc text vào video
const addOverlay = (inputPath, outputPath) => {
  let filter = '';
  if (options.addLogo && logoPath) {
    filter += `overlay=10:10:enable='between(t,0,${srtArray[srtArray.length - 1].endTime})'`;
  }
  if (options.addText) {
    filter += `${filter ? ',' : ''}drawtext=text='${
      options.customText
    }':fontcolor=white:fontsize=24:box=1:boxcolor=black@0.5:x=(w-text_w)/2:y=h-th-10`;
  }
  if (!filter) return Promise.resolve(inputPath);

  return new Promise((resolve, reject) => {
    const command = `ffmpeg -i "${inputPath}" -vf "${filter}" -c:a copy -y "${outputPath}"`;
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};
const normalizeAssPath = (assPath) => {
  // Thay \ thành /
  let p = path.resolve(assPath).replace(/\\/g, '/');
  // Nếu Windows drive letter (vd: C:/...), thêm dấu / ở đầu
  if (/^[a-zA-Z]:/.test(p)) {
    p = '/' + p;
  }
  return p;
};
const normalizeOutput = (inputPath, outputPath) => {
  return new Promise((resolve, reject) => {
    const command =
      `ffmpeg -fflags +genpts -avoid_negative_ts make_zero -i "${inputPath}" ` +
      `-c:v libx264 -preset fast -crf 18 -c:a aac -b:a 192k -ar 44100 -ac 2 -y "${outputPath}"`;
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};


// Hàm chính xử lý video
const processVideo = async () => {
  if (!videoPath) {
    alert('Vui lòng chọn file video!');
    return;
  }

  if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir);

  // 1. Tính thời lượng audio
  for (const srt of srtArray) {
    srt.duration = await getAudioDuration(srt.audioUrl);
  }
  let currentTimeline = 0;
  // 2. Cắt và xử lý từng đoạn video
  const processedSegments = [];
  for (const srt of srtArray) {
    const segmentPath = path.join(tempDir, `segment_${srt.index}_cut.mp4`);
    const adjustedPath = path.join(tempDir, `segment_${srt.index}_adjusted.mp4`);
    const finalOutputPath = path.join(tempDir, `segment_${srt.index}_adjusted_normalized.mp4`);

    // Cắt video
    await cutVideoSegment(srt, segmentPath);

    // Điều chỉnh tốc độ nếu cần và ghép audio
    await adjustVideoSpeed(segmentPath, srt.audioUrl, srt, adjustedPath);
    await normalizeOutput(adjustedPath, finalOutputPath);
    processedSegments.push(finalOutputPath);
    // Tính lại thời gian sau khi điều chỉnh
    const originalDuration = srt.endTime - srt.startTime;
    const speedFactor = Math.min(1, originalDuration / srt.duration); // như trong adjustVideoSpeed
    const newDuration = originalDuration / speedFactor;

    srt.adjustedStartTime = currentTimeline;
    srt.adjustedEndTime = currentTimeline + newDuration;
    currentTimeline += newDuration;
  }

  // 3. Tạo file ASS phụ đề
  const assPath0 = path.join(tempDir, 'subtitles.ass');
  const assPath = './subtitles.ass';
  const assRelativePath = path.relative(process.cwd(), assPath0).replace(/\\/g, '/');
  //   console.log('assRelativePath', assRelativePath);
  //   createAssFile(srtArray, assPath);
  const assContent = generateASS(srtArray);
  fs.writeFileSync(assPath, assContent);

  // 4. Ghép các đoạn video
  const concatListPath = path.join(tempDir, 'concat.txt');
  fs.writeFileSync(concatListPath, processedSegments.map((p) => `file '${p}'`).join('\n'));
  const concatOutput = path.join(tempDir, 'concatenated.mp4');
  await new Promise((resolve, reject) => {
    const command = `ffmpeg -f concat -safe 0 -i "${concatListPath}" -c copy -y "${concatOutput}"`;
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error('FFmpeg error:', stderr);
        reject(error);
      } else {
        resolve();
      }
    });
  });

  // 5. Áp dụng phụ đề
  const subbedOutput = path.join(tempDir, 'subbed.mp4');
  await new Promise((resolve, reject) => {
    // const safeAssPath = normalizeAssPath(assPath);
    const command = `ffmpeg -i "${concatOutput}" -vf "ass=${assPath}" -c:a copy -y "${subbedOutput}"`;
    exec(command, (error) => {
      if (error) reject(error);
      else resolve();
    });
  });
  // await new Promise((resolve, reject) => {
  // //   const safeAssPath = assPath.replace(/\\/g, '/');
  // const safeAssPath = normalizeAssPath(assPath);
  //   ffmpeg(concatOutput)
  //     .videoFilter(`ass=${safeAssPath}`)
  //     .audioCodec('copy')
  //     .output(subbedOutput)
  //     .on('error', (err) => {
  //       reject(err);
  //     })
  //     .on('end', () => {
  //       resolve();
  //     })
  //     .run();
  // });

  // 6. Thêm logo/text nếu có
  const finalOutput = path.join(os.homedir(), 'Desktop', 'final_video.mp4');
  await addOverlay(subbedOutput, finalOutput);

  console.log(`Video đã được render tại: ${finalOutput}`);
};

processVideo();

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
