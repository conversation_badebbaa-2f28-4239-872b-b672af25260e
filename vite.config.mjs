import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'


// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    electron([
      {
        entry: 'electron/main.js',
        vite: {
          build: {
            outDir: 'electron-build',
            minify: false,
            sourcemap: false,
            target: 'node18',
            ssr: true,
            rollupOptions: {
              external: (id) => {
                const builtins = [
                  'electron', 'path', 'fs', 'fs/promises', 'child_process',
                  'https', 'http', 'crypto', 'os', 'util', 'events', 'stream',
                  'url', 'querystring', 'dns', 'net', 'tls', 'zlib', 'readline',
                  'worker_threads', 'cluster', 'dgram', 'assert', 'buffer',
                  'constants', 'module', 'perf_hooks', 'process', 'punycode',
                  'string_decoder', 'timers', 'tty', 'v8', 'vm'
                ];
                const nativeModules = [
                  'sqlite3', 'better-sqlite3', 'playwright', 'playwright-core',
                  'puppeteer', 'puppeteer-core', 'ffmpeg-static', 'ffprobe-static', 'bytenode'
                ];
                return builtins.includes(id) || nativeModules.includes(id);
              },
              output: {
                entryFileNames: 'main.js',
                format: 'cjs',
                inlineDynamicImports: true
              }
            }
          }
        }
      },
      {
        entry: 'electron/preload.js',
        vite: {
          build: {
            outDir: 'electron-build',
            minify: false,
            sourcemap: false,
            target: 'node18',
            rollupOptions: {
              external: ['electron'],
              output: {
                entryFileNames: 'preload.js',
                format: 'cjs'
              }
            }
          }
        }
      }
    ]),
    AutoImport({
      imports: [
        'vue',
        { '@/globals/index': [['F', 'F'], ['S', 'S']] },
      ],
      dts: 'src/types/auto-imports.d.ts',
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({importStyle: false}),
      ],
      // components
      dirs: ['src/components'],
      extensions: ['vue'],
      dts: 'src/types/components.d.ts',
      include: [/\.vue$/, /\.vue\?vue/, /\.vue\.[tj]sx?\?vue/],
    }),

  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5173,
    strictPort: true,
    watch: {
      // Cho phép theo dõi thư mục cụ thể
      ignored: ['**/*', '!**/electron/**','!**/src/**']
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      }
    }
  }
})
