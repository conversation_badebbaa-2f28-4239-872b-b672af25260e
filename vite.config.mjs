import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { copyFileSync, existsSync, mkdirSync, readdirSync, statSync } from 'fs'
import { join, dirname } from 'path'

// Plugin để copy electron files
function copyElectronFiles() {
  return {
    name: 'copy-electron-files',
    writeBundle() {
      const sourceDir = 'electron'
      const targetDir = 'electron-build'

      function copyRecursive(src, dest) {
        if (!existsSync(dest)) {
          mkdirSync(dest, { recursive: true })
        }

        const items = readdirSync(src)
        for (const item of items) {
          const srcPath = join(src, item)
          const destPath = join(dest, item)

          if (statSync(srcPath).isDirectory()) {
            copyRecursive(srcPath, destPath)
          } else if (item !== 'main.js' && item !== 'preload.js') {
            // Không copy main.js và preload.js vì đã được Vite build
            copyFileSync(srcPath, destPath)
          }
        }
      }

      copyRecursive(sourceDir, targetDir)
      console.log('✅ Copied electron files to electron-build')
    }
  }
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    copyElectronFiles(),
    electron({
      entry: 'electron/main.js',
      vite: {
        build: {
          outDir: 'electron-build',
          rollupOptions: {
            external: () => true, // External tất cả dependencies
            output: {
              format: 'cjs'
            }
          }
        }
      }
    }),
    AutoImport({
      imports: [
        'vue',
        { '@/globals/index': [['F', 'F'], ['S', 'S']] },
      ],
      dts: 'src/types/auto-imports.d.ts',
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({importStyle: false}),
      ],
      // components
      dirs: ['src/components'],
      extensions: ['vue'],
      dts: 'src/types/components.d.ts',
      include: [/\.vue$/, /\.vue\?vue/, /\.vue\.[tj]sx?\?vue/],
    }),

  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5173,
    strictPort: true,
    watch: {
      // Cho phép theo dõi thư mục cụ thể
      ignored: ['**/*', '!**/electron/**','!**/src/**']
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      }
    }
  }
})
