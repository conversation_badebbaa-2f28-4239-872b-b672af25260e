const { app } = require("electron");
const path = require('path');
const fs = require('fs');
const os = require('os');
const store = require("./store");
const { default: axios } = require("axios");
const { spawn } = require("child_process");
const util = require('util');
const Database = require("./db");
const exec = util.promisify(require('child_process').exec);

// Store active processes
const activeProcesses = new Map();


const userDataPath = app?.getPath('userData') || os.homedir();
const audioTTSDir = path.join(userDataPath, 'audio-tts');
if (!fs.existsSync(audioTTSDir)) {
  fs.mkdirSync(audioTTSDir);
}
global.F ??= {}
global.S ??= {}
global.C ??= {}

F.l = console.log;


C.path = require('path');
C.fs = require('fs');
global.APP_USER_DIR = audioTTSDir
C.configDirectory = userDataPath

const HandleTranscription = path.join(userDataPath, 'HandleTranscription');
if (!fs.existsSync(HandleTranscription)) {
  fs.mkdirSync(HandleTranscription);
}

function setCurrentDir(filePath) {
  if (!filePath) {
    store.set('currentDir', audioTTSDir);
    return audioTTSDir;
  }
  const baseName = path.basename(filePath, path.extname(filePath));
  const dirPath = path.dirname(filePath);
  const outputDirectory = path.join(dirPath, `${baseName}_audio`);
  if (!fs.existsSync(outputDirectory)) {
    fs.mkdirSync(outputDirectory, { recursive: true });
  }
  store.set('currentDir', outputDirectory);
  return dirPath;
}

function getCurrentDir() {
  return store.get('currentDir');
}

// init set current dir
setCurrentDir(audioTTSDir);

function getExecutablePath(fileExcutableName) {
  if (process.platform === 'win32') {
    return path.join(__dirname, `../static/${fileExcutableName}.exe`);
  }
  // static folder
  return path.join(__dirname, `../static/${fileExcutableName}`);

}

function get_path_bin(scriptName) {
    let executablePath = '';
    const baseDir = app.isPackaged
        ? path.join(process.resourcesPath, 'app.asar.unpacked', 'static/')
        : path.join(ROOT_DIR, 'static/');

    if (os.platform() === 'win32') {
        executablePath = path.join(baseDir, `${scriptName}.exe`);
    } else {
        executablePath = path.join(baseDir, scriptName);
    }
    return executablePath;
}

async function get_tts_generator_path(){
  const baseDir = app.isPackaged ? path.join(process.resourcesPath, 'app.asar.unpacked', 'static/', 'tts_generator.exe') : path.join(ROOT_DIR, 'static/', 'tts_generator.exe');  
  return baseDir;
}

async function createUrl(voice, text) {
  const baseUrl =
    "https://api22-normal-c-useast1a.tiktokv.com/media/api/text/speech/invoke/?";
  const mapping = "&speaker_map_type=0&aid=1233";

  // Concatenate arguments
  const voiceParam = `text_speaker=${voice}`;
  const textParam = `&req_text=${text}`;
  const url = `${baseUrl}${voiceParam}${textParam}${mapping}`;
  return url;
}

async function getJson(url, sessionId) {
  const headers = {
    "User-Agent":
      "com.zhiliaoapp.musically/2022600030 (Linux; U; Android 7.1.2; es_ES; SM-G988N; Build/NRD90M;tt-ok/*********)",
    Cookie: `sessionid=${sessionId}`,
  };

  try {
    const response = await axios.post(url, null, { headers });
    return response.data;
  } catch (error) {
    throw error;
  }
}
async function execWithResult(event, executableName, args, processId = null) {
    return new Promise((resolve, reject) => {
        const id = processId || Date.now().toString();
        const child = spawn(executableName, args);

        activeProcesses.set(id, {
            process: child,
            executableName,
            args
        });

        console.log(`Process started with ID: ${id}`);

        let stdoutData = '';
        let stderrData = '';

        child.stdout.on('data', (data) => {
            const text = data.toString();
            stdoutData += text;
            console.log('stdout:', text);
        });

        child.stderr.on('data', (data) => {
            const text = data.toString();
            stderrData += text;
            console.log('stderr:', text);
        });

        child.on('exit', (code) => {
            activeProcesses.delete(id);

            const result = {
                code,
                stdout: stdoutData,
                stderr: stderrData,
                processId: id
            };

            if (code === 0) {
                console.log(`Process ${id} completed successfully`);
                resolve(result);
            } else {
                console.log(`Process ${id} exited with code ${code}`);
                reject(new Error(`Process exited with code ${code}\n${stderrData}`));
            }
        });

        child.on('error', (error) => {
            activeProcesses.delete(id);
            console.log(`Process ${id} error: ${error.message}`);
            reject(error);
        });
    });
}


async function execWithLog(event, executableName, args, processId = null) {
    const { type ='video-task', cb } = this;
    return new Promise((resolve, reject) => {
        // Generate a unique process ID if not provided
        const id = processId || Date.now().toString();
        console.log('execWithLog', executableName, args, processId);
        // Spawn the child process
        const child = spawn(executableName, args, {
            env: {
                ...process.env,
                PYTHONIOENCODING: 'utf-8'  // Ép stdout/stderr dùng UTF-8
            }
        });

        // Store the child process reference
        activeProcesses.set(id, {
            process: child,
            executableName,
            args
        });

        // Log process start
        console.log(`Process started with ID: ${id}`);

        child.stdout.on('data', (data) => {
            // event?.reply('video-task', data.toString());
            console.log('stdout', data.toString());
            event?.reply?.(type, { data: data.toString(), code: 0 });
            event?.sender?.send(type, { data: data.toString(), code: 0 });
        });

        child.stderr.on('data', (data) => {
            // event?.reply('video-task', data.toString());
            console.log('stderr', data.toString());
            event?.reply?.(type, { data: data.toString(), code: 0 });
            event?.sender?.send(type, { data: data.toString(), code: 0 });
        });

        child.on('exit', (code) => {
            // Remove from active processes
            activeProcesses.delete(id);

            if (code !== 0) {
                event?.reply?.(type, { data: `child process exited with code ${code}`, code: 0 });
                event?.sender?.send?.(type, { data: `child process exited with code ${code}`, code: 0 });
                console.log(`Process ${id} exited with code ${code}`);
                cb?.(true);
                reject(new Error(`child process exited with code ${code}`));
            } else {
                event?.reply?.(type, { data: 'Done', code: 0,success: true });
                event?.sender?.send?.(type, { data: 'Done', code: 0,success: true });
                console.log(`Process ${id} completed successfully`);
                resolve({ success: true, processId: id });
            }
            cb?.();
        });

        child.on('error', (error) => {
            // Remove from active processes
            activeProcesses.delete(id);

            event?.reply?.(type, { data: `Spawn error: ${error.message}`, code: 0 });
            event?.sender?.send?.(type, { data: `Spawn error: ${error.message}`, code: 0 });
            console.log(`Process ${id} error: ${error.message}`);
            reject(error);
        });

        // Return the process ID immediately
        resolve({ success: true, processId: id, status: 'started' });
    });
}

async function getAudioDuration(filePath) {
    const cmd = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${filePath}"`;
    const { stdout } = await exec(cmd, { encoding: 'utf8' });
    console.log('Audio duration:', stdout);
    return parseFloat(stdout.trim());
}

// s to ms
function  convertToMilliseconds(seconds) {
    return seconds * 1000;
}

// Function to stop a running process
function stopProcess(processId) {
    if (!activeProcesses.has(processId)) {
        return { success: false, error: `Process with ID ${processId} not found` };
    }

    try {
        const { process } = activeProcesses.get(processId);

        // Kill the process
        if (process && !process.killed) {
            process.kill('SIGTERM');
            console.log(`Process ${processId} terminated`);

            // Remove from active processes
            activeProcesses.delete(processId);

            return { success: true, message: `Process ${processId} terminated` };
        } else {
            return { success: false, error: `Process ${processId} already terminated` };
        }
    } catch (error) {
        console.error(`Error stopping process ${processId}:`, error);
        return { success: false, error: error.message };
    }
}

// Function to get all active processes
function getActiveProcesses() {
    const processes = [];

    for (const [id, { executableName, args }] of activeProcesses.entries()) {
        processes.push({
            processId: id,
            command: executableName,
            args: args
        });
    }

    return processes;
}


async function detectGPU() {
    return new Promise((resolve, reject) => {
        const child = spawn('ffmpeg', ['-hide_banner','-hwaccels']);
        child.stdout.on('data', (data) => {
            console.log('stdout', data.toString());
            resolve(data.toString());
        });

        child.stderr.on('data', (data) => {
            console.log('stderr',data.toString());
            reject(data.toString());
        });

        child.on('exit', (code) => {
            if (code !== 0) {
                console.log(`child process exited with code ${code}`);
                reject(new Error(`child process exited with code ${code}`));
            } else {
                resolve('video split successfully executed');
            }
        });

        child.on('error', (error) => {
            console.log(`Spawn error: ${error.message}`);
            reject(error);
        });
    });
}

// Convert video to high-quality WAV audio
async function convertVideoToWav(input, outputDir = null, processId = null) {
    if (!input || !fs.existsSync(input)) {
        return {
            success: false,
            error: "Input file does not exist"
        };
    }

    try {
        // Create output file name
        const { dir, name } = path.parse(input);
        const outputDirectory = outputDir || dir;
        const output = path.join(outputDirectory, `${name}.wav`);

        // Prepare FFmpeg arguments for high-quality WAV conversion
        const ffmpegArgs = [
            "-i", input,
            "-vn",                  // No video
            "-acodec", "pcm_s16le", // PCM 16-bit
            "-ar", "44100",         // 44.1kHz sample rate
            "-ac", "2",             // Stereo
            "-y",                   // Overwrite output file
            output
        ];

        console.log("Converting video to WAV...");
        // Start the process and get the process ID
        const result = await execWithLog(null, "ffmpeg", ffmpegArgs, processId);
        console.log('Conversion process started:', result);

        // Return the process ID and output path
        return {
            success: true,
            processId: result.processId,
            status: 'started',
            outputPath: output
        };
    } catch (err) {
        console.error("Error converting video to WAV:", err.message);
        return {
            success: false,
            error: err.message || "Unknown error occurred"
        };
    }
}

function slugifyText(text) {
  return text
    .normalize('NFD')                     // Tách dấu khỏi ký tự
    .replace(/[\u0300-\u036f]/g, '')      // Xóa các dấu
    .replace(/đ/g, 'd')                   // Chuyển đ -> d
    .replace(/Đ/g, 'd')
    .toLowerCase()
    .replace(/\s+/g, '-')                 // Thay khoảng trắng bằng -
    .replace(/[^\w\-]+/g, '')             // Xóa ký tự không phải chữ/số/gạch ngang
    .replace(/\-\-+/g, '-')               // Gộp nhiều dấu - liên tiếp
    .replace(/^-+|-+$/g, '');             // Xóa - ở đầu và cuối
}




module.exports = {
  userDataPath,
  audioTTSDir,
  setCurrentDir,
  getCurrentDir,
  getExecutablePath,
  get_path_bin,
  createUrl,
  getJson,
  execWithLog,
  detectGPU,
  stopProcess,
  getActiveProcesses,
  execWithResult,
  convertVideoToWav,
  getAudioDuration,
  convertToMilliseconds,
  slugifyText,
  get_tts_generator_path
};