const { ffmpeg } = require('./ffmpeg-config');
const { exec } = require('child_process');
const path = require('path');

async function trimAudio (inputFile, duration, outputFile) {
  return new Promise((resolve, reject) => {
    ffmpeg(inputFile)
      .setStartTime(0)
      .setDuration(duration)
      .output(outputFile)
      .on('end', () => {
        console.log('Audio trimmed successfully');
        resolve(outputFile);
      })
      .on('error', (err) => {
        console.error('Error trimming audio:', err);
        reject(err);
      })
      .run();
  });
}
async function getAudioDuration (audioPath) {
  try {
    const audioInfo = await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(audioPath, function (err, metadata) {
        if (err) {
          reject(err);
        } else {
          resolve(metadata);
        }
      });
    });
    const duration = parseFloat(audioInfo.format.duration);
    return duration;

  } catch (error) {
    console.error("Error fetching audio duration:", error);
    throw error;
  }
}
const getVideoInfo = async (event, filePath) => {
  if (!filePath) {
    throw new Error("No input specified");
  }

  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(new Error(`FFprobe error: ${err.message}`));
        return;
      }

      if (!metadata || !metadata.streams) {
        reject(new Error("Invalid video file or no metadata found"));
        return;
      }

      const videoStream = metadata.streams.find(
        (stream) => stream.codec_type === "video"
      );
      if (!videoStream) {
        reject(new Error("No video stream found in file"));
        return;
      }

      // Parse frame rate
      let fps = 0;
      if (videoStream.r_frame_rate) {
        const [num, den] = videoStream.r_frame_rate.split("/");
        fps = parseFloat(num) / parseFloat(den);
      }

      // Calculate total frames
      let totalFrames = parseInt(videoStream.nb_frames) || 0;
      if (!totalFrames && metadata.format.duration) {
        totalFrames = Math.ceil(fps * metadata.format.duration);
      }

      resolve({
        width: videoStream.width || null,
        height: videoStream.height || null,
        fps: fps,
        duration: metadata.format.duration || null,
        total_frames: totalFrames
      });
    });
  });
};
// Helper function to get appropriate encoder
function testEncoder(encoder) {
  return new Promise((resolve) => {
    exec(
      `ffmpeg -hide_banner -loglevel error -f lavfi -i testsrc=duration=1:size=515x512:rate=1 -c:v ${encoder} -f null -`,
      (error, stdout, stderr) => {
        // Lọc các lỗi nghiêm trọng như driver/gpu fail
        const criticalErrors = [
          "failed to open",         // DLL hoặc driver lỗi
          "CUDA_ERROR",             // NVIDIA CUDA lỗi
          "not found",              // encoder không tồn tại
          "Invalid argument",       // tham số sai (thường do driver lỗi)
          "No device available",    // không có GPU
          "No supported devices",   // không tìm thấy iGPU
        ];

        const output = (stdout + stderr).toLowerCase();
        const isCritical = criticalErrors.some((msg) => output.includes(msg.toLowerCase()));

        resolve(!error && !isCritical);
      }
    );
  });
}

let encoderVal = null



async function getEncoder() {
  if (encoderVal) return encoderVal;

  // Hardware encoders can be enabled later if needed
  const preferred = [
    "h264_nvenc", // NVIDIA (rời hoặc iGPU Turing+)
    "h264_amf",   // AMD iGPU
    "h264_qsv",   // Intel iGPU
    "libx264",    // CPU
  ];

  for (const encoder of preferred) {
    const ok = await testEncoder(encoder);
    if (ok) {
      console.log(`✅ Using encoder: ${encoder}`);
      encoderVal = encoder; // Lưu encoder đã chọn
      return encoder;
    } else {
      console.log(`❌ Skipping encoder: ${encoder}`);
    }
  }

  console.log("⚠ No encoder available, defaulting to libx264");
  encoderVal = "libx264"; // Mặc định nếu không có encoder nào khả dụng
  return encoderVal
}





async function getFrameVideo(event, videoPath, second, outputPath, cropData = null) {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(videoPath)
      .seekInput(second)
      .outputOptions('-frames:v 1');

    // Nếu có cropData, áp dụng filter crop
    if (cropData && cropData.length === 4) {
      const [x1, y1, x2, y2] = cropData;
      command.ffprobe((err, metadata) => {
        if (err) return reject(err);

        const width = metadata.streams[0].width;
        const height = metadata.streams[0].height;

        const cropWidth = Math.floor((x2 - x1) * width);
        const cropHeight = Math.floor((y2 - y1) * height);
        const cropX = Math.floor(x1 * width);
        const cropY = Math.floor(y1 * height);

        const cropFilter = `crop=${cropWidth}:${cropHeight}:${cropX}:${cropY}`;
        command.videoFilters(cropFilter);
        command
          .output(outputPath)
          .on('end', () => {
            console.log(`✅ Frame at ${second}s saved to ${outputPath}`);
            resolve(outputPath);
          })
          .on('error', (err) => {
            console.error('❌ Error extracting frame:', err.message);
            reject(err);
          })
          .run();
      });
    } else {
      // Nếu không crop
      command
        .output(outputPath)
        .on('end', () => {
          console.log(`✅ Frame at ${second}s saved to ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('❌ Error extracting frame:', err.message);
          reject(err);
        })
        .run();
    }
  });
}


async function convertAll(event, inputPath, outputPath) {
  const encoder = await getEncoder();
  return new Promise((resolve) => {
    ffmpeg(inputPath)
      .outputOptions([
        `-c:v ${encoder}`,
        '-preset veryfast',
        '-crf 23',
        '-c:a aac',
        '-b:a 192k',
        '-movflags +faststart',
        '-y'
      ])
      .output(outputPath)
      .on('end', () => {
        console.log('✅ Converted to Chrome-compatible format');
        event?.sender?.send('video-task', {
          data: '✅ Converted to Chrome-compatible format',
          code: 0,
        });
        resolve({ success: true });
      })
      .on('error', (err) => {
        console.error('❌ Conversion error:', err.message);
        event?.sender?.send('video-task', {
          data: `❌ Conversion error: ${err.message}`,
          code: 1,
        });
        resolve({ success: false, error: err.message });
      })
      .run();
  });
}

async function convertAnyToWav(event, inputPath, outputPath) {
  const encoder = await getEncoder();
  return new Promise((resolve) => {
    ffmpeg(inputPath)
      .outputOptions([
        `-c:v ${encoder}`, // Sử dụng encoder đã chọn
        '-acodec pcm_s16le',
        '-ar 44100',
        '-ac 2',
        '-y'
      ])
      .toFormat('wav')
      .save(outputPath)
      .on('end', () => {
        console.log('✅ Converted to WAV format');
        event?.sender?.send('audio-task', {
          data: '✅ Converted to WAV format',
          code: 0,
        });
        resolve({ success: true });
      })
      .on('error', (err) => {
        console.error('❌ Conversion error:', err.message);
        event?.sender?.send('audio-task', {
          data: `❌ Conversion error: ${err.message}`,
          code: 1,
        });
        resolve({ success: false, error: err.message });
      });
  });
}









module.exports = {
    trimAudio,
    getAudioDuration,
    getVideoInfo,
    getEncoder,
    getFrameVideo,
    convertAll,
    convertAnyToWav
}